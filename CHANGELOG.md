# Changelog

All notable changes to the K8s MCP Agent智能集群状态缓存系统 will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Planned
- 迭代2：MCP工具预加载机制
- 迭代3：单集群基础信息扫描
- 迭代4：后台定时扫描服务
- 迭代5：智能上下文增强器
- 迭代6：Gemini 2.5 Flash集成

---

## [v0.1.0] - 2024-06-18 - 迭代1：SQLite数据库设计和基础缓存类

### Added
- **SQLite数据库架构**：完整的7张表设计支持分层缓存策略
  - `clusters` - 集群基础信息表（静态资源）
  - `namespaces` - 命名空间表（静态资源）
  - `nodes` - 节点表（静态资源）
  - `pods` - Pod表（动态资源）
  - `services` - 服务表（动态资源）
  - `mcp_tools` - MCP工具映射表
  - `cache_metadata` - 缓存元数据表
- **数据模型系统**：7个核心数据模型类
  - `ClusterInfo` - 集群信息模型（`src/cache/models.py:12-43`）
  - `NamespaceInfo` - 命名空间信息模型（`src/cache/models.py:46-81`）
  - `NodeInfo` - 节点信息模型（`src/cache/models.py:84-122`）
  - `PodInfo` - Pod信息模型（`src/cache/models.py:125-163`）
  - `ServiceInfo` - 服务信息模型（`src/cache/models.py:166-204`）
  - `MCPToolInfo` - MCP工具信息模型（`src/cache/models.py:207-245`）
  - `CacheMetadata` - 缓存元数据模型（`src/cache/models.py:248-280`）
- **缓存管理器**：完整的CRUD操作实现（`src/cache/cache_manager.py`）
  - `create_record()` - 单记录创建，支持自动TTL设置
  - `get_record()` - 单记录查询，支持TTL过期检查
  - `list_records()` - 批量查询，支持过滤条件和分页
  - `update_record()` - 记录更新，自动更新时间戳
  - `delete_record()` - 记录删除，支持条件删除
  - `batch_create_records()` - 批量创建，支持事务处理
- **数据库管理器**：SQLite连接和schema管理（`src/cache/database.py`）
  - 连接池管理，支持最大10个并发连接
  - 自动schema初始化和索引创建
  - TTL过期记录清理机制

### Technical
- **分层缓存策略**：基于K8s资源变化频率的差异化TTL设计
  - 静态资源（clusters, namespaces, nodes）：30分钟TTL
  - 动态资源（pods, services）：5分钟TTL
  - 工具映射（mcp_tools）：无TTL限制，持久化存储
- **连接池架构**：线程安全的SQLite连接管理
  - 最大连接数：10个（可配置）
  - 连接超时：30秒（可配置）
  - 支持多线程并发访问，使用线程ID作为连接标识
- **JSON字段序列化**：复杂数据结构的自动处理
  - labels, annotations, containers等字段自动JSON序列化
  - 类型安全的序列化/反序列化机制
  - 支持嵌套字典和列表结构
- **Fail-Fast异常处理**：遵循现有项目架构原则
  - 继承`src/fail_fast_exceptions.py`的异常体系
  - 详细的异常上下文信息记录
  - 立即抛出异常，不进行fallback处理

### Tested
- **测试覆盖**：7个完整测试用例，100%核心功能覆盖
  - `test_database_initialization()` - 数据库初始化验证
  - `test_cluster_info_crud()` - 集群信息CRUD操作
  - `test_namespace_info_with_json_fields()` - JSON字段序列化测试
  - `test_batch_operations()` - 批量操作验证（5个Pod记录）
  - `test_mcp_tool_mapping()` - MCP工具映射功能
  - `test_ttl_functionality()` - TTL过期机制验证
  - `test_cache_statistics()` - 缓存统计功能
- **测试数据库**：126KB测试数据库成功创建（`test_data/test_k8s_cache.db`）
- **测试执行**：所有测试用例通过，运行时间0.897秒
- **数据完整性**：验证JSON字段序列化、TTL过期、批量操作的数据一致性

### Configuration
- **新增环境变量**：8个缓存系统配置项
  - `CACHE_DB_PATH` - 数据库文件路径（默认：./data/k8s_cache.db）
  - `CACHE_DB_TIMEOUT` - 数据库连接超时（默认：30秒）
  - `CACHE_DB_MAX_CONNECTIONS` - 最大连接数（默认：10）
  - `CACHE_STATIC_TTL` - 静态资源TTL（默认：1800秒/30分钟）
  - `CACHE_DYNAMIC_TTL` - 动态资源TTL（默认：300秒/5分钟）
  - `CACHE_TOOL_MAPPING_TTL` - 工具映射TTL（默认：3600秒/1小时）
- **配置验证**：启动时自动验证必需环境变量
- **配置文档**：详细配置说明记录在`doc/intelligent-cluster-cache-system.md:218-256`

### Files Added
- `src/cache/__init__.py` - 缓存模块初始化和导出
- `src/cache/database.py` - SQLite数据库管理器（287行）
- `src/cache/models.py` - 数据模型定义（285行）
- `src/cache/cache_manager.py` - 缓存管理器实现（418行）
- `test/test_cache_system.py` - 缓存系统测试套件（287行）
- `doc/intelligent-cluster-cache-system.md` - 完整技术方案文档（474行）

### Known Issues
- **DateTime警告**：Python 3.12中`datetime.utcnow()`弃用警告
  - 影响：不影响功能，仅有警告信息
  - 计划：后续版本将迁移到`datetime.now(datetime.UTC)`
- **SQLite适配器警告**：默认datetime适配器弃用警告
  - 影响：不影响功能，仅有警告信息
  - 计划：后续版本将实现自定义datetime适配器

### Performance Metrics
- **数据库大小**：126KB（测试数据）
- **测试执行时间**：0.897秒（7个测试用例）
- **内存使用**：连接池最大占用约5MB
- **并发支持**：最大10个并发数据库连接

---

## Project Information

### Architecture Overview
智能集群状态缓存系统基于现有的K8s MCP Agent项目（Gemini 2.5 Flash + MCP工具调用架构），实现分层缓存策略优化用户交互体验。

### Development Methodology
- **迭代开发**：6个迭代，每个迭代专注一个核心功能模块
- **Fail-Fast原则**：立即抛出异常，不进行fallback处理
- **测试驱动**：每个迭代包含完整的测试验证
- **环境配置**：遵循十二要素应用方法论

### Technical Stack
- **数据库**：SQLite 3.35+（零配置部署）
- **语言**：Python 3.11+
- **LLM集成**：Gemini 2.5 Flash（1M+ 输入上下文）
- **异步处理**：Python asyncio
- **配置管理**：环境变量 + .env文件

### Repository Structure
```
src/cache/          # 缓存系统核心模块
├── __init__.py     # 模块初始化
├── database.py     # SQLite数据库管理
├── models.py       # 数据模型定义
└── cache_manager.py # 缓存管理器

test/               # 测试套件
└── test_cache_system.py # 缓存系统测试

doc/                # 技术文档
└── intelligent-cluster-cache-system.md # 技术方案
```
